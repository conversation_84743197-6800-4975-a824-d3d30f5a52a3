import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Lenis from 'lenis';

gsap.registerPlugin(ScrollTrigger);

class SmoothScrollManager {
  constructor() {
    this.lenis = null;
    this.isInitialized = false;
  }

  init() {
    if (this.isInitialized) return;

    // Initialize Lenis with royal smooth scrolling
    this.lenis = new Lenis({
      duration: window.innerWidth < 768 ? 1.0 : 1.8, // Slower, more luxurious
      easing: (t) => {
        // Custom royal easing - more dramatic
        return t < 0.5
          ? 4 * t * t * t
          : 1 - Math.pow(-2 * t + 2, 3) / 2;
      },
      direction: 'vertical',
      gestureDirection: 'vertical',
      smooth: true,
      mouseMultiplier: window.innerWidth < 768 ? 0.8 : 1.2,
      smoothTouch: window.innerWidth < 768 ? true : false,
      touchMultiplier: window.innerWidth < 768 ? 2 : 2.5,
      infinite: false,
      syncTouch: true,
      syncTouchLerp: 0.15,
      wheelMultiplier: 0.8, // Slower wheel scrolling for luxury feel
    });

    // Connect Lenis with GSAP ScrollTrigger
    this.lenis.on('scroll', ScrollTrigger.update);

    gsap.ticker.add((time) => {
      this.lenis.raf(time * 1000);
    });

    gsap.ticker.lagSmoothing(0);

    this.isInitialized = true;
    this.setupScrollIndicator();
    this.setupCustomCursor();
  }

  setupScrollIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'scroll-indicator';
    document.body.appendChild(indicator);

    ScrollTrigger.create({
      trigger: document.body,
      start: 'top top',
      end: 'bottom bottom',
      onUpdate: (self) => {
        gsap.set(indicator, { scaleX: self.progress });
      },
    });
  }

  setupCustomCursor() {
    const cursor = document.createElement('div');
    const follower = document.createElement('div');
    cursor.className = 'cursor';
    follower.className = 'cursor-follower';
    
    document.body.appendChild(cursor);
    document.body.appendChild(follower);

    let mouseX = 0, mouseY = 0;
    let cursorX = 0, cursorY = 0;
    let followerX = 0, followerY = 0;

    document.addEventListener('mousemove', (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
    });

    const updateCursor = () => {
      cursorX += (mouseX - cursorX) * 0.3;
      cursorY += (mouseY - cursorY) * 0.3;
      followerX += (mouseX - followerX) * 0.1;
      followerY += (mouseY - followerY) * 0.1;

      gsap.set(cursor, { x: cursorX, y: cursorY });
      gsap.set(follower, { x: followerX, y: followerY });

      requestAnimationFrame(updateCursor);
    };

    updateCursor();

    // Magnetic effect for interactive elements
    document.querySelectorAll('button, a, .magnetic').forEach(el => {
      el.addEventListener('mouseenter', () => {
        gsap.to(cursor, { scale: 1.5, duration: 0.3 });
        gsap.to(follower, { scale: 1.2, duration: 0.3 });
      });

      el.addEventListener('mouseleave', () => {
        gsap.to(cursor, { scale: 1, duration: 0.3 });
        gsap.to(follower, { scale: 1, duration: 0.3 });
      });
    });
  }

  scrollTo(target, options = {}) {
    if (this.lenis) {
      this.lenis.scrollTo(target, {
        duration: options.duration || 1.5,
        easing: options.easing || ((t) => Math.min(1, 1.001 - Math.pow(2, -10 * t))),
        ...options
      });
    }
  }

  destroy() {
    if (this.lenis) {
      this.lenis.destroy();
      this.lenis = null;
    }
    this.isInitialized = false;
  }

  refresh() {
    if (this.lenis) {
      this.lenis.resize();
    }
    ScrollTrigger.refresh();
  }
}

export default new SmoothScrollManager();
