import React from 'react';
import { motion } from 'framer-motion';

const HeroSection = () => {
  return (
    <section id="home" className="hero-section">
      {/* Background Image */}
      <div className="hero-bg"></div>

      {/* Dark Gradient Overlay */}
      <div className="hero-overlay"></div>

      {/* Hero Content */}
      <div className="hero-content">
        {/* Main Headline */}
        <motion.h1
          className="hero-headline"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
        >
          Transforming Spaces Into Masterpieces
        </motion.h1>

        {/* Subheadline */}
        <motion.h2
          className="hero-subheadline"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 0.3, ease: "easeOut" }}
        >
          Curated Interiors | Modern Elegance | Timeless Design
        </motion.h2>

        {/* CTA Button */}
        <motion.button
          className="hero-cta-button"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.6, ease: "easeOut" }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          EXPLORE OUR WORK
        </motion.button>
      </div>
    </section>
  );
};

export default HeroSection;
