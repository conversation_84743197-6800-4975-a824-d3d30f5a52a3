import React from 'react';
import { motion } from 'framer-motion';

const Team = () => {
  return (
    <div className="team-page">
      <section id="team" className="section">
        <div className="container">
          <motion.h2
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            Our Team
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
          >
            Our talented team of designers, architects, and project managers brings together diverse expertise
            and creative vision. Each member is passionate about design excellence and committed to delivering
            exceptional results. Together, we collaborate to create spaces that inspire, function beautifully,
            and exceed expectations.
          </motion.p>
        </div>
      </section>
    </div>
  );
};

export default Team;
