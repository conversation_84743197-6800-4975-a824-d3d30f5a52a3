import React from 'react';
import { motion } from 'framer-motion';

const Services = () => {
  return (
    <div className="services-page">
      <section id="services" className="section">
        <div className="container">
          <motion.h2
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            Our Services
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
          >
            From concept to completion, we offer comprehensive interior design services including space planning,
            custom furniture design, lighting consultation, color schemes, and project management. Whether it's
            a luxury residence, corporate office, or hospitality space, we bring your vision to life with
            unparalleled creativity and precision.
          </motion.p>
        </div>
      </section>
    </div>
  );
};

export default Services;
