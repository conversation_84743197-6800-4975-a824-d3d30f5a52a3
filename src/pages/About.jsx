import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const About = () => {
  const phrases = [
    "Modern Ideas",
    "Luxury Spaces",
    "Refined Beauty",
    "Exclusive Designs"
  ];

  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);
  const [displayText, setDisplayText] = useState('');
  const [isTyping, setIsTyping] = useState(true);

  useEffect(() => {
    const currentPhrase = phrases[currentPhraseIndex];
    let timeoutId;

    if (isTyping) {
      // Typing effect
      if (displayText.length < currentPhrase.length) {
        timeoutId = setTimeout(() => {
          setDisplayText(currentPhrase.slice(0, displayText.length + 1));
        }, 100); // Typing speed
      } else {
        // Pause before erasing
        timeoutId = setTimeout(() => {
          setIsTyping(false);
        }, 2000); // Display duration
      }
    } else {
      // Erasing effect
      if (displayText.length > 0) {
        timeoutId = setTimeout(() => {
          setDisplayText(displayText.slice(0, -1));
        }, 50); // Erasing speed
      } else {
        // Move to next phrase
        setCurrentPhraseIndex((prev) => (prev + 1) % phrases.length);
        setIsTyping(true);
      }
    }

    return () => clearTimeout(timeoutId);
  }, [displayText, isTyping, currentPhraseIndex, phrases]);

  return (
    <div className="about-page">
      <section id="about" className="section">
        <div className="about-redesigned-container">

          {/* Hero Section with Split Layout */}
          <div className="about-hero-section">
            {/* Left Side - Main Content */}
            <div className="about-hero-left">
              <motion.div
                className="about-badge"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <span className="badge-text">EST. 2025</span>
              </motion.div>

              <motion.h1
                className="about-hero-title"
                initial={{ opacity: 0, y: 60 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              >
                Crafting
                <span className="title-accent typewriter">
                  {displayText}
                  <span className="cursor">|</span>
                </span>
              </motion.h1>

              <motion.p
                className="about-hero-subtitle"
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
              >
                Where luxury meets functionality, and every space tells a story of sophistication.
              </motion.p>

              <motion.div
                className="about-stats-grid"
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                viewport={{ once: true }}
              >
                <div className="stat-item">
                  <span className="stat-number">25+</span>
                  <span className="stat-label">Years Experience</span>
                </div>
                <div className="stat-item">
                  <span className="stat-number">500+</span>
                  <span className="stat-label">Projects Completed</span>
                </div>
                <div className="stat-item">
                  <span className="stat-number">100%</span>
                  <span className="stat-label">Client Satisfaction</span>
                </div>
              </motion.div>
            </div>

            {/* Right Side - Story Card */}
            <motion.div
              className="about-story-card"
              initial={{ opacity: 0, x: 60 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <div className="story-card-header">
                <h3>Our Story</h3>
                <div className="story-card-line"></div>
              </div>
              <p className="story-text">
                Founded in 2025, Elvonorr Spaces brings together 25+ years of luxury interior design expertise.
                We create spaces that transcend trends and embrace sophisticated elegance.
              </p>
              <p className="story-text">
                Every project is a collaboration, every detail a deliberate choice toward creating
                environments that inspire and reflect your unique story.
              </p>
            </motion.div>
          </div>



        </div>
      </section>
    </div>
  );
};

export default About;
