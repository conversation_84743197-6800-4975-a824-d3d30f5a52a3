import React from 'react';
import { motion } from 'framer-motion';

const Contact = () => {
  return (
    <div className="contact-page">
      <section id="contact" className="section">
        <div className="container">
          <motion.h2
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            Contact Us
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
          >
            Ready to transform your space? Let's discuss your vision and bring it to life. Contact us today
            for a consultation and discover how we can create the perfect environment for your lifestyle.
            We're here to make your design dreams a reality with personalized service and exceptional attention
            to detail.
          </motion.p>
        </div>
      </section>
    </div>
  );
};

export default Contact;
