import React, { useEffect, useRef, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useInView } from "react-intersection-observer";
import gsap from "gsap";

const testimonials = [
  {
    quote: "Elvonorr Spaces transformed our home into a modern sanctuary that perfectly reflects our lifestyle. The attention to detail was extraordinary!",
    name: "<PERSON><PERSON>",
    role: "Homeowner",
    location: "Mumbai",
    icon: "🏡",
    rating: 5,
    project: "Luxury Residential"
  },
  {
    quote: "Our office now feels so fresh and inspiring. The team was creative, professional, and delivered beyond our expectations.",
    name: "<PERSON><PERSON>",
    role: "CEO",
    location: "Delhi",
    icon: "💼",
    rating: 5,
    project: "Corporate Office"
  },
  {
    quote: "Clients love our new boutique interiors! The sophisticated design has significantly increased our foot traffic and sales.",
    name: "<PERSON><PERSON>",
    role: "Boutique Owner",
    location: "Bangalore",
    icon: "🏬",
    rating: 5,
    project: "Retail Space"
  },
  {
    quote: "The modular kitchen design is both beautiful and functional. Every inch of space is utilized perfectly. Highly recommend!",
    name: "<PERSON><PERSON>",
    role: "Homeowner",
    location: "Pune",
    icon: "🍳",
    rating: 5,
    project: "Kitchen Renovation"
  },
  {
    quote: "From concept to execution, the process was seamless and enjoyable. The team's professionalism is unmatched.",
    name: "Meera <PERSON>",
    role: "Interior Designer",
    location: "Ahmedabad",
    icon: "✨",
    rating: 5,
    project: "Residential Design"
  },
  {
    quote: "The restaurant design created the perfect ambiance for our customers. Our business has grown significantly since the renovation.",
    name: "Vik<PERSON> <PERSON>",
    role: "Restaurant Owner",
    location: "Gurgaon",
    icon: "🍽️",
    rating: 5,
    project: "Restaurant Interior"
  }
];

const Testimonials = () => {
  const [ref, inView] = useInView({
    threshold: 0.2,
    triggerOnce: true
  });

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const cardsRef = useRef([]);
  const sliderRef = useRef(null);

  useEffect(() => {
    if (inView) {
      const tl = gsap.timeline();

      // Epic title animation
      tl.fromTo(titleRef.current.children,
        { y: 100, opacity: 0, rotationX: 90 },
        {
          y: 0,
          opacity: 1,
          rotationX: 0,
          duration: 1.2,
          stagger: 0.1,
          ease: "back.out(1.7)"
        }
      )
      // Cards entrance animation
      .fromTo(cardsRef.current,
        {
          y: 150,
          opacity: 0,
          scale: 0.8,
          rotationY: 45
        },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          rotationY: 0,
          duration: 1.2,
          stagger: 0.2,
          ease: "power3.out"
        }, "-=0.8"
      );

      return () => tl.kill();
    }
  }, [inView]);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const goToTestimonial = (index) => {
    setCurrentIndex(index);
  };

  return (
    <motion.section
      ref={sectionRef}
      id="testimonials"
      className="section relative overflow-hidden"
      style={{
        background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 30%, #2a2a2a 70%, #1a1a1a 100%)',
        backgroundImage: 'url(/bg4.png)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundBlendMode: 'overlay'
      }}
      initial="hidden"
      animate={inView ? "visible" : "hidden"}
      onMouseEnter={() => setIsAutoPlaying(false)}
      onMouseLeave={() => setIsAutoPlaying(true)}
    >
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/80 via-black/40 to-black/80 z-10" />
      <div className="absolute inset-0 bg-gradient-to-r from-black/50 via-transparent to-black/50 z-10" />

      {/* Floating Elements */}
      <div className="absolute inset-0 z-10">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-accent-600/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -30, 0],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 4 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="container relative z-20">
        {/* Epic Title */}
        <div ref={titleRef} className="text-center mb-16">
          <h2 className="text-display-lg text-white mb-8 font-display">
            <span className="block">Client</span>
            <span className="block text-gradient">Testimonials</span>
          </h2>
          <p className="font-body text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            Hear what our satisfied clients say about their transformative experiences with Elvonorr Spaces
          </p>
        </div>

        {/* Modern Carousel */}
        <div className="relative max-w-6xl mx-auto">
          {/* Main Testimonial Display */}
          <div className="relative h-[500px] mb-12">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                className="absolute inset-0 flex items-center justify-center"
                initial={{ opacity: 0, x: 300, rotateY: 90 }}
                animate={{ opacity: 1, x: 0, rotateY: 0 }}
                exit={{ opacity: 0, x: -300, rotateY: -90 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
              >
                <div className="glass-elevated rounded-3xl p-12 max-w-4xl w-full text-center border-gradient">
                  {/* Quote Icon */}
                  <div className="text-6xl mb-8 text-accent-600">"</div>

                  {/* Quote */}
                  <blockquote className="font-serif text-2xl md:text-3xl text-white mb-8 leading-relaxed italic">
                    {testimonials[currentIndex].quote}
                  </blockquote>

                  {/* Rating */}
                  <div className="flex justify-center mb-6">
                    {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                      <motion.span
                        key={i}
                        className="text-accent-600 text-2xl"
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: i * 0.1 + 0.5 }}
                      >
                        ⭐
                      </motion.span>
                    ))}
                  </div>

                  {/* Client Info */}
                  <div className="flex items-center justify-center space-x-6">
                    <div className="text-6xl">{testimonials[currentIndex].icon}</div>
                    <div className="text-left">
                      <h4 className="font-display text-2xl text-white mb-1">
                        {testimonials[currentIndex].name}
                      </h4>
                      <p className="text-accent-600 font-heading text-lg">
                        {testimonials[currentIndex].role}
                      </p>
                      <p className="text-gray-400 font-body text-sm">
                        {testimonials[currentIndex].location} • {testimonials[currentIndex].project}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Navigation Controls */}
          <div className="flex items-center justify-center space-x-8 mb-8">
            <motion.button
              onClick={prevTestimonial}
              className="w-12 h-12 glass rounded-full flex items-center justify-center text-white hover:text-accent-600 transition-colors"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              ←
            </motion.button>

            <div className="flex space-x-3">
              {testimonials.map((_, index) => (
                <motion.button
                  key={index}
                  onClick={() => goToTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentIndex
                      ? 'bg-accent-600 w-8'
                      : 'bg-white/30 hover:bg-white/50'
                  }`}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.8 }}
                />
              ))}
            </div>

            <motion.button
              onClick={nextTestimonial}
              className="w-12 h-12 glass rounded-full flex items-center justify-center text-white hover:text-accent-600 transition-colors"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              →
            </motion.button>
          </div>

          {/* Thumbnail Preview */}
          <div className="flex justify-center space-x-4 overflow-x-auto pb-4">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                onClick={() => goToTestimonial(index)}
                className={`flex-shrink-0 w-24 h-24 glass rounded-xl cursor-pointer transition-all duration-300 flex items-center justify-center ${
                  index === currentIndex
                    ? 'ring-2 ring-accent-600 bg-accent-600/20'
                    : 'hover:bg-white/10'
                }`}
                whileHover={{ scale: 1.05, y: -5 }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="text-2xl">{testimonial.icon}</span>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </motion.section>
  );
};

export default Testimonials;
