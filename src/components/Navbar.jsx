import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import gsap from "gsap";

const navLinks = [
  { name: "Home", href: "#" },
  { name: "About", href: "#about" },
  { name: "Portfolio", href: "#portfolio" },
  { name: "Services", href: "#choose-category" },
  { name: "Contact", href: "#contact" },
];

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navRef = useRef(null);
  const logoRef = useRef(null);
  const linksRef = useRef([]);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    // Epic entrance animation for navbar
    const tl = gsap.timeline({ delay: 0.2 });

    tl.fromTo(navRef.current,
      { y: -100, opacity: 0 },
      { y: 0, opacity: 1, duration: 1, ease: "power3.out" }
    )
    .fromTo(logoRef.current,
      { x: -50, opacity: 0 },
      { x: 0, opacity: 1, duration: 0.8, ease: "back.out(1.7)" }, "-=0.6"
    )
    .fromTo(linksRef.current,
      { y: -20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.6, stagger: 0.1, ease: "power2.out" }, "-=0.4"
    );

    return () => tl.kill();
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <>
      <motion.nav
        ref={navRef}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
          isScrolled
            ? 'glass-elevated backdrop-blur-xl'
            : 'bg-transparent'
        }`}
        style={{
          background: isScrolled
            ? 'rgba(10, 10, 10, 0.8)'
            : 'transparent'
        }}
      >
        <div className="container mx-auto px-6 py-5 flex items-center justify-between">
          {/* Logo */}
          <motion.div
            ref={logoRef}
            className="font-display text-2xl md:text-3xl font-bold text-white cursor-pointer"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <span className="text-gradient">ELVONORR</span>
            <span className="text-white ml-2">SPACES</span>
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex space-x-12 items-center">
            {navLinks.map((link, index) => (
              <motion.a
                key={link.name}
                ref={el => linksRef.current[index] = el}
                href={link.href}
                className="relative text-white hover:text-yellow-400 transition-all duration-300 font-heading font-medium text-base tracking-wide group px-3 py-2"
                whileHover={{ y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                {link.name}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-yellow-400 to-yellow-300 transition-all duration-300 group-hover:w-full"></span>
              </motion.a>
            ))}

            <motion.button
              className="btn btn-small ml-4"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              Get Quote
            </motion.button>
          </div>

          {/* Mobile menu button */}
          <motion.button
            className="lg:hidden relative w-8 h-8 flex flex-col justify-center items-center"
            onClick={toggleMobileMenu}
            whileTap={{ scale: 0.9 }}
          >
            <motion.span
              className="w-6 h-0.5 bg-white block transition-all duration-300"
              animate={{
                rotate: isMobileMenuOpen ? 45 : 0,
                y: isMobileMenuOpen ? 0 : -4
              }}
            />
            <motion.span
              className="w-6 h-0.5 bg-white block transition-all duration-300 mt-1"
              animate={{
                opacity: isMobileMenuOpen ? 0 : 1
              }}
            />
            <motion.span
              className="w-6 h-0.5 bg-white block transition-all duration-300 mt-1"
              animate={{
                rotate: isMobileMenuOpen ? -45 : 0,
                y: isMobileMenuOpen ? -8 : 0
              }}
            />
          </motion.button>
        </div>
      </motion.nav>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            className="fixed inset-0 z-40 lg:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {/* Backdrop */}
            <motion.div
              className="absolute inset-0 bg-black/80 backdrop-blur-sm"
              onClick={toggleMobileMenu}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            />

            {/* Menu Content */}
            <motion.div
              className="absolute top-0 right-0 w-80 h-full glass-elevated"
              style={{ background: 'rgba(10, 10, 10, 0.95)' }}
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: "spring", damping: 25, stiffness: 200 }}
            >
              <div className="p-8 pt-24">
                <nav className="space-y-6">
                  {navLinks.map((link, index) => (
                    <motion.a
                      key={link.name}
                      href={link.href}
                      className="block text-white hover:text-accent-400 transition-colors duration-300 font-heading text-lg"
                      onClick={toggleMobileMenu}
                      initial={{ x: 50, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ delay: index * 0.1 + 0.2 }}
                      whileHover={{ x: 10 }}
                    >
                      {link.name}
                    </motion.a>
                  ))}

                  <motion.button
                    className="btn w-full mt-8"
                    onClick={toggleMobileMenu}
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.6 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Get Quote
                  </motion.button>
                </nav>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default Navbar;
