import React, { useEffect, useRef, useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import gsap from "gsap";

const whatsappUrl =
  "https://wa.me/91XXXXXXXXXX?text=Hi%20Elvonorr%20Spaces,%20I%20want%20to%20enquire%20about%20a%20project";

const Contact = () => {
  const [ref, inView] = useInView({
    threshold: 0.2,
    triggerOnce: true
  });

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    service: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const contactInfoRef = useRef(null);
  const formRef = useRef(null);

  const contactInfo = [
    {
      icon: "📧",
      title: "Email",
      value: "<EMAIL>",
      link: "mailto:<EMAIL>",
      gradient: "from-blue-600 to-blue-400"
    },
    {
      icon: "📱",
      title: "Phone",
      value: "+91 XXXXXXXXXX",
      link: "tel:+91XXXXXXXXXX",
      gradient: "from-green-600 to-green-400"
    },
    {
      icon: "📍",
      title: "Location",
      value: "Mumbai, India",
      link: "#",
      gradient: "from-purple-600 to-purple-400"
    },
    {
      icon: "💬",
      title: "WhatsApp",
      value: "Quick Chat",
      link: whatsappUrl,
      gradient: "from-accent-600 to-accent-400"
    }
  ];

  const services = [
    "Residential Design",
    "Commercial Design",
    "Kitchen Design",
    "Bathroom Design",
    "Office Design",
    "Retail Design",
    "Consultation",
    "Other"
  ];

  useEffect(() => {
    if (inView) {
      const tl = gsap.timeline();

      // Epic title animation
      tl.fromTo(titleRef.current.children,
        { y: 100, opacity: 0, rotationX: 90 },
        {
          y: 0,
          opacity: 1,
          rotationX: 0,
          duration: 1.2,
          stagger: 0.1,
          ease: "back.out(1.7)"
        }
      )
      // Contact info cards animation
      .fromTo(contactInfoRef.current.children,
        { x: -100, opacity: 0, scale: 0.8 },
        {
          x: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          stagger: 0.15,
          ease: "power3.out"
        }, "-=0.8"
      )
      // Form animation
      .fromTo(formRef.current,
        { x: 100, opacity: 0, rotationY: 45 },
        {
          x: 0,
          opacity: 1,
          rotationY: 0,
          duration: 1,
          ease: "power3.out"
        }, "-=0.6"
      );

      return () => tl.kill();
    }
  }, [inView]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false);
      setSubmitStatus('success');
      setFormData({
        name: '',
        email: '',
        phone: '',
        service: '',
        message: ''
      });

      setTimeout(() => setSubmitStatus(null), 3000);
    }, 2000);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };

  return (
    <motion.section
      ref={sectionRef}
      id="contact"
      className="section relative overflow-hidden"
      style={{
        background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 30%, #2a2a2a 70%, #1a1a1a 100%)'
      }}
      initial="hidden"
      animate={inView ? "visible" : "hidden"}
      variants={containerVariants}
    >
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-transparent to-black/60 z-10" />

      {/* Floating Elements */}
      <div className="absolute inset-0 z-10">
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-accent-600/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -40, 0],
              opacity: [0.2, 0.8, 0.2],
              scale: [1, 1.3, 1],
            }}
            transition={{
              duration: 5 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3,
            }}
          />
        ))}
      </div>

      <div className="container relative z-20">
        {/* Epic Title */}
        <div ref={titleRef} className="text-center mb-20">
          <h2 className="text-display-lg text-white mb-8 font-display">
            <span className="block">Let's Create</span>
            <span className="block text-gradient">Something Amazing</span>
          </h2>
          <p className="font-body text-xl md:text-2xl text-gray-200 max-w-4xl mx-auto leading-relaxed">
            Ready to transform your space? Get in touch with our team of expert designers
            and let's bring your vision to life with unparalleled craftsmanship.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">

          {/* Contact Information */}
          <div>
            <h3 className="font-display text-3xl text-white mb-12">Get In Touch</h3>

            <div ref={contactInfoRef} className="space-y-8 mb-12">
              {contactInfo.map((info, index) => (
                <motion.a
                  key={index}
                  href={info.link}
                  className="flex items-center space-x-6 group cursor-pointer"
                  whileHover={{ x: 10, scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className={`w-16 h-16 bg-gradient-to-r ${info.gradient} rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                    <span className="text-3xl">{info.icon}</span>
                  </div>
                  <div>
                    <h4 className="font-heading text-xl text-white group-hover:text-gradient transition-all duration-300">
                      {info.title}
                    </h4>
                    <p className="text-gray-200 font-body text-lg group-hover:text-white transition-colors duration-300">
                      {info.value}
                    </p>
                  </div>
                </motion.a>
              ))}
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 gap-6">
              <div className="glass p-6 rounded-xl text-center">
                <div className="text-3xl font-bold text-gradient mb-2 font-display">24/7</div>
                <div className="text-gray-200 font-heading text-sm">Support Available</div>
              </div>
              <div className="glass p-6 rounded-xl text-center">
                <div className="text-3xl font-bold text-gradient mb-2 font-display">48h</div>
                <div className="text-gray-200 font-heading text-sm">Response Time</div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <motion.div
            ref={formRef}
            className="glass-elevated p-10 rounded-3xl border-gradient"
          >
            <h3 className="font-display text-3xl mb-8 text-white">Send us a message</h3>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <motion.div
                  whileFocus={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Your Name"
                    required
                    className="w-full px-6 py-4 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-accent-600 focus:bg-white/10 transition-all duration-300 font-body"
                  />
                </motion.div>

                <motion.div
                  whileFocus={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Your Email"
                    required
                    className="w-full px-6 py-4 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-accent-600 focus:bg-white/10 transition-all duration-300 font-body"
                  />
                </motion.div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <motion.div
                  whileFocus={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="Your Phone"
                    className="w-full px-6 py-4 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-accent-600 focus:bg-white/10 transition-all duration-300 font-body"
                  />
                </motion.div>

                <motion.div
                  whileFocus={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <select
                    name="service"
                    value={formData.service}
                    onChange={handleInputChange}
                    className="w-full px-6 py-4 bg-white/5 border border-white/20 rounded-xl text-white focus:outline-none focus:border-accent-600 focus:bg-white/10 transition-all duration-300 font-body"
                  >
                    <option value="" className="bg-gray-800">Select Service</option>
                    {services.map((service) => (
                      <option key={service} value={service} className="bg-gray-800">
                        {service}
                      </option>
                    ))}
                  </select>
                </motion.div>
              </div>

              <motion.div
                whileFocus={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  placeholder="Tell us about your project..."
                  rows={5}
                  required
                  className="w-full px-6 py-4 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-accent-600 focus:bg-white/10 transition-all duration-300 resize-none font-body"
                />
              </motion.div>

              {/* Submit Status */}
              {submitStatus && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`p-4 rounded-xl text-center font-heading ${
                    submitStatus === 'success'
                      ? 'bg-green-600/20 text-green-400 border border-green-600/30'
                      : 'bg-red-600/20 text-red-400 border border-red-600/30'
                  }`}
                >
                  {submitStatus === 'success'
                    ? '✅ Message sent successfully! We\'ll get back to you soon.'
                    : '❌ Something went wrong. Please try again.'}
                </motion.div>
              )}

              <div className="flex flex-col sm:flex-row gap-4">
                <motion.button
                  type="submit"
                  disabled={isSubmitting}
                  className="btn btn-large flex-1 relative overflow-hidden"
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {isSubmitting ? (
                    <span className="flex items-center justify-center">
                      <motion.div
                        className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full mr-2"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      />
                      Sending...
                    </span>
                  ) : (
                    'Send Message'
                  )}
                </motion.button>

                <motion.a
                  href={whatsappUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn btn-secondary btn-large flex-1 text-center bg-green-600 hover:bg-green-700 border-green-600"
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                >
                  💬 WhatsApp
                </motion.a>
              </div>
            </form>
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
};

export default Contact;
