import React, { useEffect, useRef } from "react";
import { motion } from "framer-motion";
import gsap from "gsap";

const Hero = () => {
  const heroRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const buttonsRef = useRef(null);
  const scrollIndicatorRef = useRef(null);
  const backgroundRef = useRef(null);

  useEffect(() => {
    const tl = gsap.timeline({ delay: 0.5 });

    // Epic entrance animation
    tl.fromTo(backgroundRef.current,
      { scale: 1.2, opacity: 0 },
      { scale: 1, opacity: 1, duration: 2, ease: "power2.out" }
    )
    .fromTo(titleRef.current.children,
      { y: 100, opacity: 0, rotationX: 90 },
      {
        y: 0,
        opacity: 1,
        rotationX: 0,
        duration: 1.2,
        stagger: 0.1,
        ease: "back.out(1.7)"
      }, "-=1.5"
    )
    .fromTo(subtitleRef.current,
      { y: 50, opacity: 0 },
      { y: 0, opacity: 1, duration: 1, ease: "power2.out" }, "-=0.8"
    )
    .fromTo(buttonsRef.current.children,
      { y: 30, opacity: 0, scale: 0.8 },
      {
        y: 0,
        opacity: 1,
        scale: 1,
        duration: 0.8,
        stagger: 0.2,
        ease: "back.out(1.7)"
      }, "-=0.6"
    )
    .fromTo(scrollIndicatorRef.current,
      { y: 20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.8, ease: "power2.out" }, "-=0.4"
    );

    // Continuous floating animation for scroll indicator
    gsap.to(scrollIndicatorRef.current, {
      y: 10,
      duration: 2,
      repeat: -1,
      yoyo: true,
      ease: "power2.inOut"
    });

    // Parallax effect on scroll
    const handleScroll = () => {
      const scrolled = window.pageYOffset;
      const rate = scrolled * -0.5;

      if (backgroundRef.current) {
        gsap.set(backgroundRef.current, {
          transform: `translateY(${rate}px)`
        });
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      tl.kill();
    };
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  return (
    <motion.section
      ref={heroRef}
      className="section relative overflow-hidden"
      style={{ minHeight: '100vh' }}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      {/* Epic Background */}
      <div
        ref={backgroundRef}
        className="absolute inset-0 z-0"
        style={{
          background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 30%, #2a2a2a 70%, #1a1a1a 100%)',
          backgroundImage: 'url(/wallpaper.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundBlendMode: 'overlay',
          minHeight: '100vh'
        }}
      />

      {/* Gradient Overlays */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/30 to-black/60 z-10" />
      <div className="absolute inset-0 bg-gradient-to-r from-black/40 via-transparent to-black/40 z-10" />

      {/* Animated Particles */}
      <div className="absolute inset-0 z-10">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.2, 0.8, 0.2],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="container relative z-20 text-center">
        {/* Epic Title */}
        <div ref={titleRef} className="mb-8">
          <h1 className="text-display-xl mb-4 font-display" style={{ color: '#ffffff', fontSize: 'clamp(3rem, 8vw, 6rem)', fontWeight: 'bold' }}>
            <span className="block" style={{ color: '#ffffff' }}>ELVONORR</span>
            <span className="block text-gradient" style={{ background: 'linear-gradient(135deg, #d4af37 0%, #f4e4a6 50%, #d4af37 100%)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>SPACES</span>
          </h1>
        </div>

        {/* Subtitle */}
        <motion.p
          ref={subtitleRef}
          className="font-body text-xl md:text-2xl mb-12 max-w-4xl mx-auto leading-relaxed"
          style={{ color: '#e5e5e5', fontSize: 'clamp(1.2rem, 3vw, 1.5rem)' }}
        >
          Transforming spaces into extraordinary experiences through innovative design,
          timeless elegance, and unparalleled craftsmanship
        </motion.p>

        {/* CTA Buttons */}
        <motion.div
          ref={buttonsRef}
          className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16"
        >
          <motion.button
            className="btn btn-large text-lg"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            Explore Our Work
          </motion.button>

          <motion.button
            className="btn btn-secondary btn-large text-lg"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            Get Consultation
          </motion.button>
        </motion.div>

        {/* Scroll Indicator */}
        <motion.div
          ref={scrollIndicatorRef}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <div className="text-white/70 text-center">
            <p className="text-sm mb-3 font-body">Scroll to explore</p>
            <div className="w-px h-12 bg-gradient-to-b from-white/50 to-transparent mx-auto"></div>
            <motion.div
              className="w-2 h-2 bg-white/50 rounded-full mx-auto mt-2"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
          </div>
        </motion.div>
      </div>
    </motion.section>
  );
};

export default Hero;
