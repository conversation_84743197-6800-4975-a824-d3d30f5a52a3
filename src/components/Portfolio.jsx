import React, { useEffect, useRef, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useInView } from "react-intersection-observer";
import gsap from "gsap";

const projects = [
  {
    title: "Luxury Residential",
    icon: "🏠",
    desc: "Elegant homes designed for modern living with timeless appeal and sophisticated aesthetics",
    category: "Residential",
    image: "/bg1.png",
    gradient: "from-accent-600 to-accent-400",
    stats: { area: "2,500 sq ft", duration: "3 months" }
  },
  {
    title: "Corporate Offices",
    icon: "🏢",
    desc: "Professional spaces that inspire productivity and creativity in the modern workplace",
    category: "Commercial",
    image: "/bg2.png",
    gradient: "from-blue-600 to-blue-400",
    stats: { area: "5,000 sq ft", duration: "4 months" }
  },
  {
    title: "Retail Spaces",
    icon: "🛍️",
    desc: "Stunning retail environments that enhance customer experience and drive sales",
    category: "Retail",
    image: "/bg3.png",
    gradient: "from-purple-600 to-purple-400",
    stats: { area: "1,800 sq ft", duration: "2 months" }
  },
  {
    title: "Hospitality",
    icon: "🏨",
    desc: "Welcoming spaces that create memorable experiences for guests and visitors",
    category: "Hospitality",
    image: "/bg1.png",
    gradient: "from-green-600 to-green-400",
    stats: { area: "8,000 sq ft", duration: "6 months" }
  },
  {
    title: "Restaurants",
    icon: "🍽️",
    desc: "Dining spaces that complement culinary excellence with atmospheric design",
    category: "F&B",
    image: "/bg2.png",
    gradient: "from-red-600 to-red-400",
    stats: { area: "3,200 sq ft", duration: "3 months" }
  },
  {
    title: "Wellness Centers",
    icon: "🧘",
    desc: "Tranquil environments for health and wellness that promote peace and serenity",
    category: "Wellness",
    image: "/bg3.png",
    gradient: "from-teal-600 to-teal-400",
    stats: { area: "4,500 sq ft", duration: "4 months" }
  }
];

const Portfolio = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  const [selectedProject, setSelectedProject] = useState(null);
  const [filter, setFilter] = useState('All');

  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const cardsRef = useRef([]);
  const filterRef = useRef(null);

  const categories = ['All', 'Residential', 'Commercial', 'Retail', 'Hospitality', 'F&B', 'Wellness'];

  const filteredProjects = filter === 'All'
    ? projects
    : projects.filter(project => project.category === filter);

  useEffect(() => {
    if (inView) {
      const tl = gsap.timeline();

      // Epic title animation
      tl.fromTo(titleRef.current.children,
        { y: 100, opacity: 0, rotationX: 90 },
        {
          y: 0,
          opacity: 1,
          rotationX: 0,
          duration: 1.2,
          stagger: 0.1,
          ease: "back.out(1.7)"
        }
      )
      // Filter buttons animation
      .fromTo(filterRef.current.children,
        { y: 30, opacity: 0, scale: 0.8 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: "back.out(1.7)"
        }, "-=0.8"
      )
      // Cards with epic 3D entrance
      .fromTo(cardsRef.current,
        {
          y: 200,
          opacity: 0,
          scale: 0.5,
          rotationY: 60,
          z: -300
        },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          rotationY: 0,
          z: 0,
          duration: 1.8,
          stagger: 0.15,
          ease: "power3.out"
        }, "-=0.6"
      );

      return () => tl.kill();
    }
  }, [inView, filteredProjects]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  return (
    <>
      <motion.section
        ref={sectionRef}
        id="portfolio"
        className="section relative overflow-hidden"
        style={{
          background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 30%, #2a2a2a 70%, #1a1a1a 100%)',
          backgroundImage: 'url(/bg3.png)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundBlendMode: 'overlay'
        }}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        variants={containerVariants}
      >
        {/* Enhanced Background Effects */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/80 via-black/50 to-black/80 z-10" />
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-transparent to-black/60 z-10" />

        {/* Animated Background Particles */}
        <div className="absolute inset-0 z-10">
          {[...Array(30)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-accent-600/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -50, 0],
                opacity: [0.2, 0.8, 0.2],
                scale: [1, 1.5, 1],
              }}
              transition={{
                duration: 6 + Math.random() * 4,
                repeat: Infinity,
                delay: Math.random() * 4,
              }}
            />
          ))}
        </div>

        {/* Main Content */}
        <div className="container relative z-20">
          {/* Epic Title */}
          <div ref={titleRef} className="text-center mb-16">
            <h2 className="text-display-lg text-white mb-8 font-display">
              <span className="block">Our</span>
              <span className="block text-gradient">Portfolio</span>
            </h2>
            <p className="font-body text-xl md:text-2xl text-gray-200 max-w-4xl mx-auto leading-relaxed">
              Discover our diverse range of interior design projects, each crafted with precision,
              passion, and unparalleled attention to detail
            </p>
          </div>

          {/* Filter Buttons */}
          <div ref={filterRef} className="flex flex-wrap justify-center gap-4 mb-16">
            {categories.map((category) => (
              <motion.button
                key={category}
                onClick={() => setFilter(category)}
                className={`px-6 py-3 rounded-full font-heading text-sm transition-all duration-300 ${
                  filter === category
                    ? 'bg-gradient-to-r from-yellow-600 to-yellow-400 text-black font-semibold'
                    : 'glass text-gray-200 hover:text-white hover:bg-white/10'
                }`}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                {category}
              </motion.button>
            ))}
          </div>

          {/* Projects Grid */}
          <AnimatePresence mode="wait">
            <motion.div
              key={filter}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              transition={{ duration: 0.5, stagger: 0.1 }}
            >
              {filteredProjects.map((project, index) => (
                <motion.div
                  key={`${filter}-${index}`}
                  ref={el => cardsRef.current[index] = el}
                  className="group cursor-pointer perspective-1000"
                  whileHover={{
                    scale: 1.03,
                    rotateY: 5,
                    z: 50
                  }}
                  whileTap={{ scale: 0.97 }}
                  onClick={() => setSelectedProject(project)}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <div className="glass-elevated rounded-3xl overflow-hidden h-[500px] relative border-gradient transform-gpu">
                    {/* Project Image */}
                    <div
                      className="h-64 bg-cover bg-center relative"
                      style={{
                        backgroundImage: `url(${project.image})`,
                        backgroundBlendMode: 'overlay'
                      }}
                    >
                      <div className="absolute inset-0 bg-black/50 group-hover:bg-black/30 transition-all duration-500" />
                      <div className={`absolute inset-0 bg-gradient-to-br ${project.gradient} opacity-20 group-hover:opacity-30 transition-opacity duration-500`} />

                      {/* Category Badge */}
                      <div className="absolute top-4 left-4">
                        <span className={`bg-gradient-to-r ${project.gradient} text-white px-4 py-2 rounded-full text-sm font-heading font-semibold shadow-lg`}>
                          {project.category}
                        </span>
                      </div>

                      {/* Icon */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <motion.div
                          className="text-8xl group-hover:scale-110 transition-transform duration-500"
                          whileHover={{ rotateY: 360 }}
                          transition={{ duration: 0.8 }}
                        >
                          {project.icon}
                        </motion.div>
                      </div>

                      {/* Hover Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 flex items-end justify-center pb-6">
                        <motion.button
                          className="btn btn-small bg-white text-black hover:bg-accent-600 hover:text-white"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          View Details
                        </motion.button>
                      </div>
                    </div>

                    {/* Project Info */}
                    <div className="p-8 relative">
                      <h3 className="font-display text-2xl mb-4 text-white group-hover:text-gradient transition-all duration-500">
                        {project.title}
                      </h3>
                      <p className="text-gray-200 mb-6 leading-relaxed font-body">
                        {project.desc}
                      </p>

                      {/* Project Stats */}
                      <div className="flex justify-between items-center text-sm text-gray-200 mb-6">
                        <div className="flex items-center space-x-4">
                          <span className="font-heading">📐 {project.stats.area}</span>
                          <span className="font-heading">⏱️ {project.stats.duration}</span>
                        </div>
                      </div>

                      {/* Shimmer Effect */}
                      <div className="absolute inset-0 shimmer opacity-0 group-hover:opacity-100 transition-opacity duration-700 pointer-events-none" />
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </AnimatePresence>
        </div>
      </motion.section>

      {/* Project Modal */}
      <AnimatePresence>
        {selectedProject && (
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedProject(null)}
          >
            {/* Backdrop */}
            <motion.div
              className="absolute inset-0 bg-black/80 backdrop-blur-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            />

            {/* Modal Content */}
            <motion.div
              className="relative glass-elevated rounded-3xl p-8 max-w-2xl w-full max-h-[80vh] overflow-y-auto"
              initial={{ scale: 0.8, opacity: 0, y: 50 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.8, opacity: 0, y: 50 }}
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={() => setSelectedProject(null)}
                className="absolute top-4 right-4 w-8 h-8 bg-white/10 rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-colors"
              >
                ✕
              </button>

              <div className="text-center">
                <div className="text-8xl mb-6">{selectedProject.icon}</div>
                <h3 className="font-display text-4xl text-white mb-4">{selectedProject.title}</h3>
                <p className="text-gray-300 text-lg leading-relaxed mb-6">{selectedProject.desc}</p>

                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="glass p-4 rounded-xl">
                    <div className="text-2xl font-bold text-gradient mb-1">{selectedProject.stats.area}</div>
                    <div className="text-gray-400 text-sm">Total Area</div>
                  </div>
                  <div className="glass p-4 rounded-xl">
                    <div className="text-2xl font-bold text-gradient mb-1">{selectedProject.stats.duration}</div>
                    <div className="text-gray-400 text-sm">Duration</div>
                  </div>
                </div>

                <button className="btn btn-large w-full">
                  Contact for Similar Project
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default Portfolio;
