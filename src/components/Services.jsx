import React, { useEffect, useRef } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

const services = [
  { title: "Modular Kitchen", icon: "37d" },
  { title: "Living Room", icon: "6cb" },
  { title: "Bedroom Design", icon: "6cf" },
  { title: "Office Spaces", icon: "3e2" },
  { title: "Retail Interiors", icon: "6cd" },
  { title: "Custom Furniture", icon: "691" },
];

const Services = () => {
  const cardsRef = useRef([]);

  useEffect(() => {
    gsap.fromTo(
      cardsRef.current,
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 0.6,
        stagger: 0.2,
        ease: "power3.out",
        scrollTrigger: {
          trigger: cardsRef.current[0],
          start: "top 80%",
        },
      }
    );
  }, []);

  return (
    <section className="relative w-full min-h-[40vh] py-20 px-4 flex justify-center items-center bg-[#2f2f2f] my-16 overflow-hidden">
      <div className="relative glass max-w-6xl w-full p-8 sm:p-10 rounded-2xl border border-white/30 shadow-xl backdrop-blur-[18px] flex flex-col items-center" style={{ background: 'rgba(255,255,255,0.18)' }}>
        {/* Glossy overlay */}
        <div className="absolute left-0 top-0 w-full h-1/2 rounded-t-2xl pointer-events-none" style={{ background: 'linear-gradient(120deg,rgba(255,255,255,0.45) 0%,rgba(255,255,255,0.12) 80%)', zIndex: 2 }} />
        <div className="relative z-10 w-full">
          <h2 className="font-serif text-3xl md:text-4xl text-center mb-4 text-white">Our Services</h2>
          <p className="text-lg text-center mb-12 font-sans text-white">
            We design stunning residential and commercial interiors
          </p>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 w-full">
            {services.map((service, idx) => (
              <div
                key={service.title}
                ref={el => (cardsRef.current[idx] = el)}
                className="glass flex flex-col items-center p-8 rounded-xl border border-white/20 backdrop-blur-[12px] bg-white/10"
              >
                <div className="text-5xl mb-4 select-none text-white" aria-hidden="true">
                  {service.icon}
                </div>
                <h3 className="font-serif text-xl font-semibold mb-2 text-center text-white">
                  {service.title}
                </h3>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
