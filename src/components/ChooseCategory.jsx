import React, { useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import gsap from "gsap";

const categories = [
  {
    title: "Residential Spaces",
    desc: "Transform your home into a sanctuary of comfort and style with our residential design expertise that reflects your personality.",
    icon: "🏠",
    to: "/residential",
    gradient: "from-accent-600 via-accent-500 to-accent-400",
    features: ["Living Rooms", "Bedrooms", "Kitchens", "Bathrooms"]
  },
  {
    title: "Commercial Spaces",
    desc: "Create inspiring work environments that boost productivity and reflect your brand values with professional design solutions.",
    icon: "🏢",
    to: "/commercial",
    gradient: "from-primary-400 via-primary-300 to-primary-200",
    features: ["Offices", "Retail", "Restaurants", "Hotels"]
  },
];

const ChooseCategory = ({ id }) => {
  const [ref, inView] = useInView({
    threshold: 0.2,
    triggerOnce: true
  });

  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const cardsRef = useRef([]);

  useEffect(() => {
    if (inView) {
      const tl = gsap.timeline();

      // Epic title animation
      tl.fromTo(titleRef.current.children,
        { y: 100, opacity: 0, rotationX: 90 },
        {
          y: 0,
          opacity: 1,
          rotationX: 0,
          duration: 1.2,
          stagger: 0.1,
          ease: "back.out(1.7)"
        }
      )
      // Epic card animations with zoom and rotation
      .fromTo(cardsRef.current,
        {
          y: 150,
          opacity: 0,
          scale: 0.6,
          rotationY: 45,
          z: -200
        },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          rotationY: 0,
          z: 0,
          duration: 1.5,
          stagger: 0.3,
          ease: "power3.out"
        }, "-=0.8"
      );

      return () => tl.kill();
    }
  }, [inView]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  return (
    <motion.section
      ref={sectionRef}
      id={id}
      className="section relative overflow-hidden"
      style={{
        background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 30%, #2a2a2a 70%, #1a1a1a 100%)',
        backgroundImage: 'url(/bg2.png)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundBlendMode: 'overlay'
      }}
      initial="hidden"
      animate={inView ? "visible" : "hidden"}
      variants={containerVariants}
    >
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/80 via-black/40 to-black/80 z-10" />
      <div className="absolute inset-0 bg-gradient-to-r from-black/50 via-transparent to-black/50 z-10" />

      {/* Animated Background Elements */}
      <div className="absolute inset-0 z-10">
        {[...Array(25)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-accent-600/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -40, 0],
              opacity: [0.3, 0.8, 0.3],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 5 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="container relative z-20 text-center">
        {/* Epic Title */}
        <div ref={titleRef} className="mb-20">
          <h2 className="text-display-lg text-white mb-8 font-display">
            <span className="block">Choose Your</span>
            <span className="block text-gradient">Space</span>
          </h2>
          <p className="font-body text-xl md:text-2xl text-gray-200 max-w-4xl mx-auto leading-relaxed">
            Whether you're looking to transform your home or create an inspiring workspace,
            we have the expertise to bring your vision to life with unparalleled craftsmanship.
          </p>
        </div>

        {/* Epic Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 max-w-6xl mx-auto">
          {categories.map((category, index) => (
            <motion.div
              key={index}
              ref={el => cardsRef.current[index] = el}
              className="group perspective-1000"
              whileHover={{
                scale: 1.02,
                rotateY: 5,
                z: 50
              }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              <Link to={category.to} className="block">
                <div className="glass-elevated rounded-3xl p-10 h-[500px] flex flex-col justify-between relative overflow-hidden border-gradient transform-gpu">
                  {/* Card Background Gradient */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${category.gradient} opacity-5 group-hover:opacity-10 transition-opacity duration-500`} />

                  {/* Shimmer Effect */}
                  <div className="absolute inset-0 shimmer opacity-0 group-hover:opacity-100 transition-opacity duration-700" />

                  <div className="relative z-10">
                    {/* Icon */}
                    <motion.div
                      className="text-9xl mb-8 group-hover:scale-110 transition-transform duration-500"
                      whileHover={{ rotateY: 360 }}
                      transition={{ duration: 0.8 }}
                    >
                      {category.icon}
                    </motion.div>

                    {/* Title */}
                    <h3 className="font-display text-4xl mb-6 text-white group-hover:text-gradient transition-all duration-500">
                      {category.title}
                    </h3>

                    {/* Description */}
                    <p className="text-gray-200 leading-relaxed mb-8 font-body text-lg">
                      {category.desc}
                    </p>

                    {/* Features */}
                    <div className="grid grid-cols-2 gap-3 mb-8">
                      {category.features.map((feature, idx) => (
                        <motion.div
                          key={idx}
                          className="text-sm text-gray-200 bg-white/5 rounded-lg px-3 py-2 font-heading"
                          whileHover={{ scale: 1.05, backgroundColor: "rgba(255,255,255,0.1)" }}
                        >
                          {feature}
                        </motion.div>
                      ))}
                    </div>
                  </div>

                  {/* CTA Button */}
                  <motion.div
                    className={`btn w-full bg-gradient-to-r ${category.gradient} text-white border-none relative overflow-hidden`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <span className="relative z-10">
                      Explore {category.title.split(' ')[0]}
                    </span>
                  </motion.div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      </div>
    </motion.section>
  );
};

export default ChooseCategory; 