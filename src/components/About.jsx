import React, { useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

const About = () => {
  const [ref, inView] = useInView({
    threshold: 0.2,
    triggerOnce: true
  });

  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const contentRef = useRef(null);
  const statsRef = useRef(null);
  const imageRef = useRef(null);
  const horizontalRef = useRef(null);

  useEffect(() => {
    if (inView) {
      const tl = gsap.timeline();

      // Epic entrance animations
      tl.fromTo(titleRef.current,
        { x: -100, opacity: 0 },
        { x: 0, opacity: 1, duration: 1.2, ease: "power3.out" }
      )
      .fromTo(contentRef.current.children,
        { x: -50, opacity: 0 },
        { x: 0, opacity: 1, duration: 0.8, stagger: 0.2, ease: "power2.out" }, "-=0.8"
      )
      .fromTo(statsRef.current.children,
        { y: 50, opacity: 0, scale: 0.8 },
        { y: 0, opacity: 1, scale: 1, duration: 0.6, stagger: 0.1, ease: "back.out(1.7)" }, "-=0.4"
      )
      .fromTo(imageRef.current,
        { x: 100, opacity: 0, rotationY: 45 },
        { x: 0, opacity: 1, rotationY: 0, duration: 1, ease: "power3.out" }, "-=1"
      );

      // Horizontal scroll effect for features
      if (horizontalRef.current) {
        gsap.to(horizontalRef.current, {
          x: -200,
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top bottom",
            end: "bottom top",
            scrub: 1,
          }
        });
      }

      return () => {
        tl.kill();
        ScrollTrigger.getAll().forEach(trigger => trigger.kill());
      };
    }
  }, [inView]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };

  const features = [
    { icon: "🎨", title: "Creative Design", desc: "Innovative and artistic approach" },
    { icon: "⚡", title: "Fast Delivery", desc: "Quick turnaround times" },
    { icon: "🏆", title: "Premium Quality", desc: "Luxury materials and finishes" },
    { icon: "🤝", title: "Client Focus", desc: "Personalized service experience" },
  ];

  return (
    <motion.section
      ref={sectionRef}
      id="about"
      className="section-compact relative overflow-hidden"
      style={{
        background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 30%, #2a2a2a 70%, #1a1a1a 100%)',
        backgroundImage: 'url(/bg1.png)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundBlendMode: 'overlay'
      }}
      initial="hidden"
      animate={inView ? "visible" : "hidden"}
      variants={containerVariants}
    >
      {/* Enhanced Background Overlays */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/40 to-black/70 z-10" />
      <div className="absolute inset-0 bg-gradient-to-r from-black/30 via-transparent to-black/30 z-10" />

      {/* Floating Elements */}
      <div className="absolute inset-0 z-10">
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-accent-600/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -30, 0],
              opacity: [0.2, 0.6, 0.2],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 4 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="container relative z-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">

          {/* Text Content */}
          <div>
            <motion.h2
              ref={titleRef}
              className="text-heading-xl text-white mb-8 font-display"
            >
              About <span className="text-gradient">Elvonorr Spaces</span>
            </motion.h2>

            <div ref={contentRef} className="space-y-6 mb-12">
              <p className="font-body text-lg md:text-xl text-white leading-relaxed">
                With over two decades of experience in luxury interior design, Elvonorr Spaces
                has established itself as a premier destination for those seeking exceptional
                design solutions that transcend ordinary expectations.
              </p>

              <p className="font-body text-lg md:text-xl text-gray-200 leading-relaxed">
                We believe that every space tells a story. Our mission is to help you tell
                yours through thoughtful design, premium materials, and unparalleled attention
                to detail that creates lasting impressions.
              </p>

              <p className="font-body text-lg text-gray-300 leading-relaxed">
                From concept to completion, we craft environments that not only meet your functional
                needs but also inspire and elevate your daily experiences.
              </p>
            </div>

            {/* Stats */}
            <div ref={statsRef} className="grid grid-cols-2 gap-8">
              <div className="text-center glass p-6 rounded-xl">
                <div className="text-4xl font-bold text-gradient mb-2 font-display">500+</div>
                <div className="text-gray-200 font-heading text-sm">Projects Completed</div>
              </div>
              <div className="text-center glass p-6 rounded-xl">
                <div className="text-4xl font-bold text-gradient mb-2 font-display">20+</div>
                <div className="text-gray-200 font-heading text-sm">Years Experience</div>
              </div>
            </div>
          </div>

          {/* Visual Content */}
          <motion.div
            ref={imageRef}
            className="relative"
          >
            <div className="glass-elevated p-8 rounded-3xl border-gradient">
              <div className="aspect-square bg-gradient-to-br from-accent-600/20 via-accent-500/10 to-accent-400/20 rounded-2xl flex items-center justify-center relative overflow-hidden">
                {/* Background Pattern */}
                <div className="absolute inset-0 opacity-10">
                  <div className="absolute inset-0 bg-gradient-to-br from-accent-600 to-transparent"></div>
                </div>

                <div className="text-center relative z-10">
                  <div className="text-8xl mb-6 filter drop-shadow-lg">🏛️</div>
                  <h3 className="font-display text-3xl text-white mb-4">Our Philosophy</h3>
                  <p className="text-gray-300 font-body leading-relaxed max-w-xs">
                    Creating timeless spaces that inspire, elevate, and transform the way you live and work
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Horizontal Scrolling Features */}
        <div className="mt-20 overflow-hidden">
          <h3 className="text-heading-lg text-white mb-8 text-center font-display">
            Why Choose <span className="text-gradient">Elvonorr</span>
          </h3>
          <div ref={horizontalRef} className="flex space-x-8 w-max">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                className="glass p-6 rounded-xl min-w-[280px] text-center"
                whileHover={{ scale: 1.05, y: -5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="text-5xl mb-4">{feature.icon}</div>
                <h4 className="font-heading text-xl text-white mb-2">{feature.title}</h4>
                <p className="text-gray-400 font-body text-sm">{feature.desc}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </motion.section>
  );
};

export default About;