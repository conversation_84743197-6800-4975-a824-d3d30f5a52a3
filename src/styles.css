/* ELVONORR SPACES - Premium Interior Design */

/* Import Premium Fonts */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&family=Cormorant+Garamond:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Cinzel:wght@400;500;600;700;800;900&family=Trajan+Pro:wght@400;700&family=Optima:wght@400;700&family=Copperplate:wght@400;700&family=Old+Standard+TT:wght@400;700&family=Crimson+Text:wght@400;600;700&family=EB+Garamond:wght@400;500;600;700;800&family=Lora:wght@400;500;600;700&family=Merriweather:wght@300;400;700;900&display=swap');

/* CSS Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#root {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: #333;
  overflow-x: hidden;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  position: relative;
}

.App {
  position: relative;
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

/* ===== NAVBAR STYLES ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background: transparent;
  backdrop-filter: none;
  border-bottom: none;
  box-shadow: none;
  transition: all 0.3s ease;
  height: 60px;
}

.navbar-scrolled {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-container {
  width: 100%;
  margin: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  height: 60px;
}

.navbar-brand {
  font-family: 'Playfair Display', serif;
  font-size: 1.3rem;
  font-weight: 700;
  color: #1a1a1a;
  text-decoration: none;
  letter-spacing: 1px;
  transition: color 0.3s ease;
  white-space: nowrap;
  padding-left: 1rem;
}

.navbar-brand:hover {
  color: #d4af37;
}

.nav-links {
  display: flex;
  gap: 2rem;
  align-items: center;
  padding-right: 1rem;
}

.nav-link {
  font-family: 'Inter', sans-serif;
  font-size: 0.85rem;
  font-weight: 500;
  color: #333333;
  text-decoration: none;
  padding: 0.3rem 0;
  position: relative;
  transition: color 0.3s ease;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: #d4af37;
  transition: width 0.3s ease;
}

.nav-link:hover {
  color: #d4af37;
}

.nav-link:hover::after {
  width: 100%;
}

/* Mobile Menu */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
}

.hamburger {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.hamburger span {
  width: 25px;
  height: 2px;
  background: #333333;
  transition: all 0.3s ease;
  border-radius: 1px;
}

.hamburger.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger.active span:nth-child(2) {
  opacity: 0;
}

.hamburger.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.mobile-nav-link {
  display: block;
  padding: 1rem 2rem;
  color: #333333;
  text-decoration: none;
  font-family: 'Inter', sans-serif;
  font-size: 1rem;
  font-weight: 500;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.mobile-nav-link:last-child {
  border-bottom: none;
}

.mobile-nav-link:hover {
  background: rgba(212, 175, 55, 0.1);
  color: #d4af37;
}

/* Mobile Menu Button */
.mobile-menu-btn {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  gap: 4px;
}

.hamburger-line {
  width: 25px;
  height: 2px;
  background: #ffffff;
  transition: all 0.3s ease;
  transform-origin: center;
}

.hamburger-line.active:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger-line.active:nth-child(2) {
  opacity: 0;
}

.hamburger-line.active:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation */
.mobile-nav {
  display: none;
  overflow: hidden;
  background: rgba(20, 20, 20, 0.98);
  backdrop-filter: blur(20px);
}

.mobile-nav-open {
  display: block;
}

.mobile-nav-link {
  display: block;
  padding: 1rem 2rem;
  color: #ffffff;
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.mobile-nav-link:hover {
  background: rgba(212, 175, 55, 0.1);
  color: #d4af37;
}

/* ===== HERO SECTION STYLES ===== */
.hero-section {
  position: relative;
  width: 100%;
  height: 100vh;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

/* Hero Background */
.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/wallpaper.jpg');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  z-index: -2;
}

/* Hero Overlay */
.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  z-index: -1;
}



/* Hero Content */
.hero-content {
  position: relative;
  z-index: 10;
  text-align: center;
  max-width: 900px;
  padding: 0 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

/* Hero Headline */
.hero-headline {
  font-family: 'Cinzel', serif;
  font-size: 60px;
  font-weight: 800;
  color: #F9F6F0;
  text-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
  line-height: 1.2;
  margin: 0;
  text-align: center;
  letter-spacing: 2px;
  text-transform: uppercase;
}

/* Hero Subheadline */
.hero-subheadline {
  font-family: 'Old Standard TT', serif;
  font-size: 20px;
  font-weight: 700;
  color: #E6E6E6;
  margin: 0;
  text-align: center;
  line-height: 1.4;
  letter-spacing: 1.5px;
  text-transform: uppercase;
}

/* Hero CTA Button */
.hero-cta-button {
  padding: 12px 32px;
  font-family: 'Cinzel', serif;
  font-size: 14px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 2px;
  border: 2px solid #D4AF37;
  color: #FFFFFF;
  background: transparent;
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
}

.hero-cta-button:hover {
  background: #D4AF37;
  color: #1A1A1A;
  transform: scale(1.05);
  font-weight: 800;
}

/* Hero Buttons */
.hero-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 1rem 2.5rem;
  font-family: 'Inter', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5px;
  backdrop-filter: blur(20px);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.6s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #d4af37, #f4d03f);
  color: #000;
  border: 2px solid transparent;
  box-shadow:
    0 8px 25px rgba(212, 175, 55, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow:
    0 15px 35px rgba(212, 175, 55, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 2px solid rgba(212, 175, 55, 0.5);
  backdrop-filter: blur(20px);
}

.btn-secondary:hover {
  background: rgba(212, 175, 55, 0.2);
  border-color: #d4af37;
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(212, 175, 55, 0.2);
  color: #d4af37;
}

/* ===== SECTION STYLES ===== */
.section {
  position: relative;
  width: 100vw;
  height: 100vh;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

/* About Section */
.about-page .section {
  background-image: url('/bg1.png');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}

/* Services Section */
.services-page .section {
  background-image: url('/bg2.png');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}

/* Team Section */
.team-page .section {
  background-image: url('/bg3.png');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}

/* Contact Section */
.contact-page .section {
  background-image: url('/bg4.png');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}



/* Section Content */
.section .container {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 900px;
  padding: 0 2rem;
  color: #ffffff;
}

.section h2 {
  font-family: 'Playfair Display', serif;
  font-size: clamp(2.5rem, 6vw, 4.5rem);
  font-weight: 800;
  margin-bottom: 1.5rem;
  color: #d4af37;
  text-shadow:
    0 0 20px rgba(212, 175, 55, 0.6),
    2px 2px 8px rgba(0, 0, 0, 0.8);
}

.section p {
  font-family: 'Cormorant Garamond', serif;
  font-size: clamp(1.1rem, 2.5vw, 1.6rem);
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
  line-height: 1.6;
}

/* ===== REDESIGNED ABOUT SECTION STYLES ===== */
.about-redesigned-container {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 4rem 6rem;
  z-index: 10;
  box-sizing: border-box;
  gap: 4rem;
}

/* About Hero Section */
.about-hero-section {
  display: grid;
  grid-template-columns: 1.4fr 0.6fr;
  gap: 4rem;
  width: 100%;
  max-width: 1600px;
  align-items: center;
}

.about-hero-left {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* About Badge */
.about-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: fit-content;
  padding: 0.8rem 2rem;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.15), rgba(212, 175, 55, 0.05));
  border: 2px solid rgba(212, 175, 55, 0.3);
  border-radius: 50px;
  backdrop-filter: blur(10px);
}

.badge-text {
  font-family: 'Cinzel', serif;
  font-size: 14px;
  font-weight: 700;
  color: #D4AF37;
  letter-spacing: 2px;
  text-transform: uppercase;
}

/* About Hero Title */
.about-hero-title {
  font-family: 'Cinzel', serif;
  font-size: clamp(3.5rem, 7vw, 6rem);
  font-weight: 900;
  color: #8C2D19;
  line-height: 1.1;
  margin: 0;
  padding-top: 4rem;
  padding-left: 6rem;
  text-shadow: 0 4px 12px rgba(140, 45, 25, 0.2);
}

.title-accent {
  color: #D4AF37;
  display: block;
  text-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.typewriter {
  position: relative;
  min-height: 1.2em; /* Prevent layout shift */
}

.cursor {
  display: inline-block;
  background-color: #D4AF37;
  width: 3px;
  margin-left: 2px;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* About Hero Subtitle */
.about-hero-subtitle {
  font-family: 'Lora', serif;
  font-size: clamp(1.1rem, 2vw, 1.4rem);
  font-weight: 400;
  color: #2C2C2C;
  line-height: 1.6;
  margin: 0;
  opacity: 0.9;
  padding-left: 6rem;
}

/* About Stats Grid */
.about-stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
}

.stat-number {
  font-family: 'Cinzel', serif;
  font-size: 2.5rem;
  font-weight: 900;
  color: #D4AF37;
  line-height: 1;
  text-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
}

.stat-label {
  font-family: 'Lora', serif;
  font-size: 0.9rem;
  font-weight: 500;
  color: #2C2C2C;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.8;
}

/* About Story Card */
.about-story-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(248, 248, 248, 0.1));
  backdrop-filter: blur(2px);
  border-radius: 32px;
  padding: 4rem;
  padding-top: 30rem;
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.08),
    0 10px 20px rgba(215, 13, 13, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(212, 175, 55, 0.3);
  position: relative;
  overflow: hidden;
}

.about-story-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #D4AF37, #B8911A, #D4AF37);
  border-radius: 32px 32px 0 0;
}

.story-card-header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.story-card-header h3 {
  font-family: 'Cinzel', serif;
  font-size: 4rem;
  font-weight: 800;
  color: #8C2D19;
  margin: 0;
  text-shadow: 0 2px 4px rgba(140, 45, 25, 0.1);
}

.story-card-line {
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #D4AF37, #B8911A);
  border-radius: 2px;
}

.story-text {
  font-family: 'Lora', serif;
  font-size: 1.1rem;
  font-weight: 400;
  color: #6c0b0b;
  line-height: 1.8;
  margin: 0 0 1.5rem 0;
  text-align: justify;
}

.story-text:last-child {
  margin-bottom: 0;
}

/* About Services Preview Section */
.about-services-preview {
  width: 100%;
  max-width: 1400px;
  display: flex;
  flex-direction: column;
  gap: 3rem;
  align-items: center;
}

.services-preview-title {
  font-family: 'Cinzel', serif;
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 800;
  color: #8C2D19;
  text-align: center;
  margin: 0;
  text-shadow: 0 3px 8px rgba(140, 45, 25, 0.2);
}

.services-preview-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  width: 100%;
}

.service-preview-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 248, 248, 0.8));
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2.5rem 2rem;
  text-align: center;
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.08),
    0 5px 15px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(212, 175, 55, 0.15);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.service-preview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #D4AF37, #B8911A);
  transform: scaleX(0);
  transition: transform 0.4s ease;
  border-radius: 24px 24px 0 0;
}

.service-preview-card:hover::before {
  transform: scaleX(1);
}

.service-preview-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.12),
    0 10px 25px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-color: rgba(212, 175, 55, 0.3);
}

.service-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
}

.service-preview-card h4 {
  font-family: 'Cinzel', serif;
  font-size: 1.3rem;
  font-weight: 700;
  color: #8C2D19;
  margin: 0 0 1rem 0;
  text-shadow: 0 1px 3px rgba(140, 45, 25, 0.1);
}

.service-preview-card p {
  font-family: 'Lora', serif;
  font-size: 1rem;
  font-weight: 400;
  color: #2C2C2C;
  line-height: 1.6;
  margin: 0;
  opacity: 0.9;
}

/* ===== RESPONSIVE STYLES FOR REDESIGNED ABOUT ===== */

/* Tablet Styles */
@media (max-width: 1024px) {
  .about-redesigned-container {
    padding: 3rem 4rem;
    gap: 3rem;
  }

  .about-hero-section {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .about-hero-left {
    align-items: center;
  }

  .about-stats-grid {
    justify-content: center;
    max-width: 600px;
  }

  .services-preview-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    max-width: 600px;
    margin: 0 auto;
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .about-redesigned-container {
    padding: 2rem;
    gap: 2.5rem;
  }

  .about-hero-title {
    font-size: clamp(2rem, 8vw, 3rem);
  }

  .about-stats-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    text-align: center;
  }

  .stat-item {
    align-items: center;
  }

  .about-story-card {
    padding: 3rem;
    padding-top: 8rem;
  }

  .services-preview-title {
    font-size: clamp(1.5rem, 6vw, 2rem);
  }

  .service-preview-card {
    padding: 2rem 1.5rem;
  }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
  .about-redesigned-container {
    padding: 1.5rem;
    gap: 2rem;
  }

  .about-badge {
    padding: 0.6rem 1.5rem;
  }

  .badge-text {
    font-size: 12px;
  }

  .about-hero-subtitle {
    font-size: 1rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .stat-label {
    font-size: 0.8rem;
  }

  .about-story-card {
    padding: 2.5rem;
    padding-top: 6rem;
  }

  .story-card-header h3 {
    font-size: 1.5rem;
  }

  .story-text {
    font-size: 1rem;
  }

  .service-preview-card {
    padding: 1.5rem;
  }

  .service-icon {
    font-size: 2.5rem;
    height: 60px;
  }

  .service-preview-card h4 {
    font-size: 1.1rem;
  }

  .service-preview-card p {
    font-size: 0.9rem;
  }
}

/* About Quote Card - Hidden for cleaner layout */
.about-quote-card {
  display: none;
}



/* ===== RESPONSIVE STYLES ===== */



@media (max-width: 768px) {

}

/* Tablet Styles */
@media (max-width: 1024px) {
  .navbar-container {
    padding: 0;
    height: 60px;
  }

  .navbar-brand {
    font-size: 1.8rem;
    letter-spacing: 3px;
  }

  .navbar-brand::before,
  .navbar-brand::after {
    display: none;
  }

  .desktop-nav {
    gap: 2rem;
  }

  .nav-link {
    font-size: 1rem;
    padding: 0.6rem 1.2rem;
  }

  .hero-content {
    max-width: 800px;
    padding: 0 1.5rem;
    gap: 1.5rem;
  }

  .hero-headline {
    font-size: 48px;
    font-weight: 800;
    text-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
    letter-spacing: 1.5px;
  }

  .hero-subheadline {
    font-size: 18px;
    font-weight: 700;
    letter-spacing: 1.2px;
  }

  .hero-cta-button {
    padding: 10px 28px;
    font-size: 13px;
  }


}

/* Mobile Styles */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .mobile-menu-btn {
    display: flex;
  }

  .navbar-container {
    padding: 0;
    height: 60px;
  }

  .navbar-brand {
    font-size: 1.5rem;
    white-space: nowrap;
  }
}

@media (max-width: 480px) {
  .navbar-container {
    padding: 0;
    height: 60px;
  }

  .navbar-brand {
    font-size: 1.3rem;
    white-space: nowrap;
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .hero-content {
    padding: 0 1rem;
    gap: 1.5rem;
  }

  .hero-headline {
    font-size: 36px;
    font-weight: 800;
    text-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
    letter-spacing: 1px;
  }

  .hero-subheadline {
    font-size: 16px;
    font-weight: 700;
    letter-spacing: 1px;
  }

  .hero-cta-button {
    padding: 10px 24px;
    font-size: 12px;
  }


}

/* Small Mobile Styles */
@media (max-width: 480px) {
  .navbar {
    top: 5px;
    width: 99%;
  }

  .navbar-container {
    padding: 0;
    height: 60px;
  }

  .navbar-brand {
    font-size: 1rem;
    white-space: nowrap;
  }

  .hero-content {
    padding: 0 0.8rem;
    gap: 1rem;
  }

  .hero-headline {
    font-size: 28px;
    font-weight: 800;
    text-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
    letter-spacing: 0.8px;
  }

  .hero-subheadline {
    font-size: 14px;
    font-weight: 700;
    letter-spacing: 0.8px;
  }

  .hero-cta-button {
    padding: 8px 20px;
    font-size: 11px;
  }


}

/* High Resolution Displays */
@media (min-width: 1400px) {
  .navbar-container {
    padding: 0;
    height: 60px;
  }

  .navbar-brand {
    font-size: 2.4rem;
  }

  .nav-link {
    font-size: 1.2rem;
    padding: 0.9rem 1.8rem;
  }

  .hero-content {
    max-width: 1000px;
    gap: 2.5rem;
  }

  .hero-headline {
    font-size: 72px;
    font-weight: 800;
    text-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
    letter-spacing: 2.5px;
  }

  .hero-subheadline {
    font-size: 24px;
    font-weight: 700;
    letter-spacing: 1.8px;
  }

  .hero-cta-button {
    padding: 14px 36px;
    font-size: 15px;
  }
}

/* Animations and Effects */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: 200% 200%;
  }
  50% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 200% 200%;
  }
}

@keyframes subtleGlow {
  0%, 100% {
    text-shadow:
      0 0 30px rgba(212, 175, 55, 0.4),
      0 0 60px rgba(212, 175, 55, 0.2),
      2px 2px 8px rgba(0, 0, 0, 0.9),
      4px 4px 16px rgba(0, 0, 0, 0.6);
  }
  50% {
    text-shadow:
      0 0 40px rgba(212, 175, 55, 0.6),
      0 0 80px rgba(212, 175, 55, 0.3),
      2px 2px 8px rgba(0, 0, 0, 0.9),
      4px 4px 16px rgba(0, 0, 0, 0.6);
  }
}

/* Smooth scrolling for anchor links */
html {
  scroll-padding-top: 80px;
}
